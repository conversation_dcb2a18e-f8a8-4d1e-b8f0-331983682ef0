﻿using Core.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace Core.DataAccess.EntityFramework
{
    public class EfEntityRepositoryBase<TEntity, TContext> : IEntityRepository<TEntity>
        where TEntity : class, IEntity, new()
        where TContext : DbContext, new()
    {


        public void Add(TEntity entity)
        {
            using (TContext context = new TContext())
            {

                var addedEntity = context.Entry(entity);
                addedEntity.State = EntityState.Added;
                if (addedEntity.Properties.Any(p => p.Metadata.Name == "CreationDate"))
                {
                    addedEntity.Property("CreationDate").CurrentValue = DateTime.Now;
                }
                if (addedEntity.Properties.Any(p => p.Metadata.Name == "AssignedDate"))
                {
                    addedEntity.Property("AssignedDate").CurrentValue = DateTime.Now;
                }
                if (addedEntity.Properties.Any(p => p.Metadata.Name == "DeletedDate"))
                {
                    addedEntity.Property("DeletedDate").IsModified = false;
                }
                if (addedEntity.Properties.Any(p => p.Metadata.Name == "UpdatedDate"))
                {
                    addedEntity.Property("DeletedDate").IsModified = false;
                }
                if (addedEntity.Properties.Any(p => p.Metadata.Name == "IsActive"))
                {
                    addedEntity.Property("IsActive").CurrentValue = true;
                }
                context.SaveChanges();
            }
        }

        public void Delete(object id)
        {
            using (TContext context = new TContext())
            {
                TEntity entity = context.Set<TEntity>().Find(id);
                if (entity == null) return;

                var deletedEntity = context.Entry(entity);
                deletedEntity.State = EntityState.Modified;

                if (deletedEntity.Properties.Any(p => p.Metadata.Name == "CreationDate"))
                {
                    deletedEntity.Property("CreationDate").IsModified = false;
                }
                if (deletedEntity.Properties.Any(p => p.Metadata.Name == "AssignedDate"))
                {
                    deletedEntity.Property("AssignedDate").IsModified = false;
                }
                if (deletedEntity.Properties.Any(p => p.Metadata.Name == "DeletedDate"))
                {
                    deletedEntity.Property("DeletedDate").CurrentValue = DateTime.Now;
                }
                if (deletedEntity.Properties.Any(p => p.Metadata.Name == "UpdatedDate"))
                {
                    deletedEntity.Property("UpdatedDate").IsModified = false;
                }
                if (deletedEntity.Properties.Any(p => p.Metadata.Name == "IsActive"))
                {
                    deletedEntity.Property("IsActive").CurrentValue = false;
                }

                context.SaveChanges();
            }
        }

        public void HardDelete(object id)
        {
            using (TContext context = new TContext())
            {
                TEntity entity = context.Set<TEntity>().Find(id);
                if (entity == null) return;

                var deletedEntity = context.Entry(entity);
                deletedEntity.State = EntityState.Deleted;

                context.SaveChanges();
            }
        }




        public TEntity Get(Expression<Func<TEntity, bool>> filter)
        {
            using (TContext context = new TContext())
            {
                return context.Set<TEntity>().SingleOrDefault(filter);
            }
        }

        public List<TEntity> GetAll(Expression<Func<TEntity, bool>> filter = null)
        {
            using (TContext context = new TContext())
            {
                return filter == null
                    ? context.Set<TEntity>().ToList()
                    : context.Set<TEntity>().Where(filter).ToList();
            }
        }

        public void Update(TEntity entity)
        {
            using (TContext context = new TContext())
            {

                var updatedEntity = context.Entry(entity);
                updatedEntity.State = EntityState.Modified;
                if (updatedEntity.Properties.Any(p => p.Metadata.Name == "CreationDate"))
                {
                    updatedEntity.Property("CreationDate").IsModified = false;
                }
                if (updatedEntity.Properties.Any(p => p.Metadata.Name == "AssignedDate"))
                {
                    updatedEntity.Property("AssignedDate").IsModified = false;
                }
                if (updatedEntity.Properties.Any(p => p.Metadata.Name == "DeletedDate"))
                {
                    updatedEntity.Property("DeletedDate").IsModified = false;
                }
                if (updatedEntity.Properties.Any(p => p.Metadata.Name == "UpdatedDate"))
                {
                    updatedEntity.Property("UpdatedDate").CurrentValue = DateTime.Now;
                }
                context.SaveChanges();
            }
        }
    }
}
