-- MemberWorkoutPrograms AssignedDate Constraint Düzeltmesi
-- C# DateTime.Now ile SQL GETDATE() arasındaki timing farkları için 5 saniye tolerans ekleniyor

USE [GymProject]
GO

-- Mevcut constraint'i kaldır ve yenisini ekle
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_MemberWorkoutPrograms_AssignedDate')
BEGIN
    ALTER TABLE [dbo].[MemberWorkoutPrograms]
    DROP CONSTRAINT [CK_MemberWorkoutPrograms_AssignedDate]
END

ALTER TABLE [dbo].[MemberWorkoutPrograms]
ADD CONSTRAINT [CK_MemberWorkoutPrograms_AssignedDate] 
CHECK ([AssignedDate] <= DATEADD(SECOND, 5, GETDATE()))
GO
