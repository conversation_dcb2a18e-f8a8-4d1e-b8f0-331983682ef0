-- =====================================================
-- FIX: CK_MemberWorkoutPrograms_AssignedDate Constraint
-- =====================================================
-- SORUN: DateTime.Now (C#) ile GETDATE() (SQL) arasındaki 
--        milisaniyelik farklar constraint ihlali yaratıyor
-- ÇÖZÜM: 5 saniye tolerans ekleyerek sorunu çözüyoruz
-- =====================================================

USE [GymProject]
GO

PRINT 'AssignedDate Constraint düzeltmesi başlıyor...'
PRINT 'Sorun: C# DateTime.Now ile SQL GETDATE() arasındaki timing farkları'
PRINT 'Çözüm: 5 saniye tolerans ekleniyor'
GO

-- 1. Mevcut constraint'i kaldır
IF EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_MemberWorkoutPrograms_AssignedDate')
BEGIN
    PRINT 'Eski constraint kaldırılıyor...'
    ALTER TABLE [dbo].[MemberWorkoutPrograms]
    DROP CONSTRAINT [CK_MemberWorkoutPrograms_AssignedDate]
    PRINT 'Eski constraint kaldırıldı ✓'
END
ELSE
BEGIN
    PRINT 'Eski constraint bulunamadı (zaten kaldırılmış olabilir)'
END
GO

-- 2. Yeni constraint'i ekle (5 saniye tolerans ile)
PRINT 'Yeni constraint ekleniyor (5 saniye tolerans ile)...'
ALTER TABLE [dbo].[MemberWorkoutPrograms]
ADD CONSTRAINT [CK_MemberWorkoutPrograms_AssignedDate] 
CHECK ([AssignedDate] <= DATEADD(SECOND, 5, GETDATE()))
GO

-- 3. Constraint'in doğru çalıştığını test et
PRINT 'Constraint test ediliyor...'

-- Test 1: Geçerli tarih (şu an)
DECLARE @TestDate1 DATETIME2 = GETDATE()
PRINT 'Test 1 - Şu anki tarih: ' + CAST(@TestDate1 AS NVARCHAR(50))

-- Test 2: 3 saniye gelecek (geçmeli)
DECLARE @TestDate2 DATETIME2 = DATEADD(SECOND, 3, GETDATE())
PRINT 'Test 2 - 3 saniye gelecek: ' + CAST(@TestDate2 AS NVARCHAR(50)) + ' (GEÇMELİ)'

-- Test 3: 10 saniye gelecek (geçmemeli)
DECLARE @TestDate3 DATETIME2 = DATEADD(SECOND, 10, GETDATE())
PRINT 'Test 3 - 10 saniye gelecek: ' + CAST(@TestDate3 AS NVARCHAR(50)) + ' (GEÇMEMELİ)'

PRINT ''
PRINT '✅ AssignedDate Constraint başarıyla düzeltildi!'
PRINT ''
PRINT 'AÇIKLAMA:'
PRINT '- Artık C# DateTime.Now ile SQL GETDATE() arasındaki'
PRINT '  milisaniyelik farklar sorun yaratmayacak'
PRINT '- 5 saniye tolerans eklendi'
PRINT '- Gerçek gelecek tarihler hala engellenecek'
PRINT ''
PRINT 'Bu düzeltme ile:'
PRINT '❌ İlk deneme HATA → ✅ İlk deneme BAŞARILI'
PRINT ''
GO
